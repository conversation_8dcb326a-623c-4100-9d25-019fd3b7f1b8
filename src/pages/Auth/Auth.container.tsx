import React, { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAppDispatch, useAppSelector } from '../../store/hooks'
import { loginUser, signupUser, googleAuthUser, clearError } from '../../store/slices/authSlice'
import AuthComponent from './Auth.component'
import {
  AuthContainerProps,
  AuthMode,
  AuthFormData,
  AuthFormErrors,
  LoginFormData,
  SignupFormData
} from './types'

/**
 * Auth Container - Main container that handles authentication logic and state
 */
const AuthContainer: React.FC<AuthContainerProps> = ({ mode: initialMode = 'login' }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useAppDispatch()
  const { user, isAuthenticated, isLoading, error } = useAppSelector((state) => state.auth)

  // State
  const [mode, setMode] = useState<AuthMode>(initialMode)
  const [formData, setFormData] = useState<AuthFormData>(() => {
    if (initialMode === 'signup') {
      return {
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        full_name: '',
      } as SignupFormData
    } else {
      return {
        emailOrUsername: '',
        password: '',
      } as LoginFormData
    }
  })
  const [formErrors, setFormErrors] = useState<AuthFormErrors>({})

  // Get client ID from environment
  const getClientId = () => {
    return import.meta.env.VITE_DEFAULT_TENANT_SLUG || 'test'
  }

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      // Check if onboarding is completed
      if (user.onboarding_completed === false) {
        navigate('/onboarding', { replace: true })
      } else {
        const from = location.state?.from?.pathname || '/dashboard'
        navigate(from, { replace: true })
      }
    }
  }, [isAuthenticated, user, navigate, location])

  // Clear errors when auth error changes
  useEffect(() => {
    if (error) {
      setFormErrors(prev => ({ ...prev, general: error }))
    }
  }, [error])

  // Reset form when mode changes
  useEffect(() => {
    if (mode === 'signup') {
      setFormData({
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        full_name: '',
      } as SignupFormData)
    } else {
      setFormData({
        emailOrUsername: '',
        password: '',
      } as LoginFormData)
    }
    setFormErrors({})
  }, [mode])

  // Handle mode change
  const handleModeChange = (newMode: AuthMode) => {
    setMode(newMode)

    // Immediately update form data to match the new mode
    if (newMode === 'signup') {
      setFormData({
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        full_name: '',
      } as SignupFormData)
    } else {
      setFormData({
        emailOrUsername: '',
        password: '',
      } as LoginFormData)
    }
    setFormErrors({})
  }

  // Handle input changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear field-specific error when user starts typing
    if (formErrors[field as keyof AuthFormErrors]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }))
    }
    
    // Clear general error when user starts typing
    if (formErrors.general) {
      setFormErrors(prev => ({ ...prev, general: undefined }))
      dispatch(clearError())
    }
  }

  // Validate form
  const validateForm = (): boolean => {
    const errors: AuthFormErrors = {}

    if (mode === 'login') {
      const loginData = formData as LoginFormData

      // Email or Username validation for login
      if (!loginData.emailOrUsername.trim()) {
        errors.emailOrUsername = 'Email or username is required'
      }
    } else {
      const signupData = formData as SignupFormData

      // Email validation for signup
      if (!signupData.email.trim()) {
        errors.email = 'Email is required'
      } else if (!/\S+@\S+\.\S+/.test(signupData.email)) {
        errors.email = 'Please enter a valid email address'
      }

      // Username validation for signup
      if (!signupData.username.trim()) {
        errors.username = 'Username is required'
      } else if (signupData.username.length < 3) {
        errors.username = 'Username must be at least 3 characters'
      }

      // Confirm password validation for signup
      if (!signupData.confirmPassword.trim()) {
        errors.confirmPassword = 'Please confirm your password'
      } else if (signupData.password !== signupData.confirmPassword) {
        errors.confirmPassword = 'Passwords do not match'
      }
    }

    // Password validation (common for both)
    if (!formData.password.trim()) {
      errors.password = 'Password is required'
    } else if (formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      if (mode === 'signup') {
        const signupData = formData as SignupFormData
        await dispatch(signupUser({
          username: signupData.username.trim(),
          email: signupData.email.trim(),
          password: signupData.password,
          full_name: signupData.full_name?.trim(),
          client_id: getClientId(),
        })).unwrap()
      } else {
        const loginData = formData as LoginFormData
        await dispatch(loginUser({
          email: loginData.emailOrUsername.trim(),  // API expects email field but we send username or email
          password: loginData.password,
          client_id: getClientId(),
        })).unwrap()
      }
      // Navigation will be handled by useEffect when isAuthenticated changes
    } catch (error) {
      // Error handling is done in Redux slice
      console.error('Authentication failed:', error)
    }
  }

  // Handle Google authentication
  const handleGoogleAuth = async (credential: string) => {
    try {
      await dispatch(googleAuthUser({
        id_token: credential,
        client_id: getClientId(),
      })).unwrap()
      // Navigation will be handled by useEffect when isAuthenticated changes
    } catch (error) {
      console.error('Google authentication failed:', error)
    }
  }

  // Handle clear error
  const handleClearError = () => {
    setFormErrors(prev => ({ ...prev, general: undefined }))
    dispatch(clearError())
  }

  // Don't render auth form if user is already authenticated (after all hooks)
  if (isAuthenticated && user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <AuthComponent
      mode={mode}
      formData={formData}
      errors={formErrors}
      isLoading={isLoading}
      onModeChange={handleModeChange}
      onInputChange={handleInputChange}
      onSubmit={handleSubmit}
      onGoogleAuth={handleGoogleAuth}
      onClearError={handleClearError}
    />
  )
}

export default AuthContainer
