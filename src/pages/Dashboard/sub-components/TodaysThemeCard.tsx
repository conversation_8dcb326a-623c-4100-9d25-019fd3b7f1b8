import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { 
  <PERSON>rkles, 
  Calendar, 
  ArrowRight, 
  Loader2, 
  AlertCircle,
  Star,
  Crown,
  Zap
} from 'lucide-react'
import { cn } from '../../../utils/cn'
import { CuratedService } from '../../../services/curatedService'

interface TodaysThemeCardProps {
  cardVariants: any
}

interface TodaysTheme {
  curated_set_id: string
  task_set_id: string
  theme_color: string
  title: string
  theme_id: string
}

const TodaysThemeCard: React.FC<TodaysThemeCardProps> = ({ cardVariants }) => {
  const [theme, setTheme] = useState<TodaysTheme | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isDarkTheme, setIsDarkTheme] = useState(() => {
    // Initialize from localStorage to persist theme preference
    const saved = localStorage.getItem('today-theme-dark')
    return saved !== null ? JSON.parse(saved) : false
  })
  const navigate = useNavigate()

  useEffect(() => {
    fetchTodaysTheme()
  }, [])

  const fetchTodaysTheme = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await CuratedService.getTodaysTheme()
      setTheme(response.data)
    } catch (err) {
      console.error('Error fetching today\'s theme:', err)
      setError('Failed to load today\'s theme')
    } finally {
      setLoading(false)
    }
  }

  const handleNavigateToTasks = () => {
    if (theme?.task_set_id) {
      navigate(`/tasks/${theme.task_set_id}`)
    }
  }

  const handleToggleTheme = () => {
    const newTheme = !isDarkTheme
    setIsDarkTheme(newTheme)
    // Persist the user's theme preference
    localStorage.setItem('today-theme-dark', JSON.stringify(newTheme))
  }

  const getFancyWords = () => {
    const words = [
      "Today's Featured Quest",
      "Daily Learning Adventure",
      "Today's Special Challenge",
      "Featured Theme of the Day",
      "Today's Learning Journey",
      "Daily Discovery Theme",
      "Today's Spotlight Challenge"
    ]
    return words[Math.floor(Math.random() * words.length)]
  }

  const getColorCombination = () => {
    if (!theme) return { primary: '#6366f1', secondary: '#8b5cf6' }

    if (isDarkTheme) {
      // Dark theme colors - deeper, more muted
      return {
        primary: '#1f2937', // gray-800
        secondary: '#374151'  // gray-700
      }
    } else {
      // Light theme colors - use theme color
      return {
        primary: theme.theme_color,
        secondary: theme.theme_color + 'CC'
      }
    }
  }

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 border border-border rounded-xl p-6"
      >
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-8 h-8 animate-spin text-purple-500" />
          <span className="ml-3 text-purple-600 dark:text-purple-400">
            Loading today's theme...
          </span>
        </div>
      </motion.div>
    )
  }

  if (error || !theme) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950/20 dark:to-orange-950/20 border border-red-200 dark:border-red-800 rounded-xl p-6"
      >
        <div className="flex items-center justify-center py-8 text-red-600 dark:text-red-400">
          <AlertCircle className="w-6 h-6" />
          <span className="ml-3">{error || 'No theme available today'}</span>
        </div>
      </motion.div>
    )
  }

  const colors = getColorCombination()

  return (
    <motion.div
      variants={cardVariants}
      className={cn(
        "relative overflow-hidden rounded-xl border transition-all duration-300",
        isDarkTheme
          ? "border-gray-700 bg-gradient-to-br from-gray-800 to-gray-900"
          : "border-border bg-gradient-to-br from-white to-gray-50"
      )}
      style={{
        background: isDarkTheme
          ? `linear-gradient(135deg, ${colors.primary}25, ${colors.secondary}15)`
          : `linear-gradient(135deg, ${colors.primary}15, ${colors.primary}05)`
      }}
    >
      {/* Decorative background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div
          className={cn(
            "absolute -top-4 -right-4 w-20 h-20 rounded-full transition-opacity duration-300",
            isDarkTheme ? "opacity-20" : "opacity-10"
          )}
          style={{ backgroundColor: colors.primary }}
        />
        <div
          className={cn(
            "absolute -bottom-6 -left-6 w-28 h-28 rounded-full transition-opacity duration-300",
            isDarkTheme ? "opacity-15" : "opacity-5"
          )}
          style={{ backgroundColor: colors.secondary }}
        />
      </div>

      <div className="relative p-5">
        {/* Header with title and theme toggle - Better organized */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <motion.div
                animate={{
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <Crown
                  className="w-5 h-5"
                  style={{ color: colors.primary }}
                />
              </motion.div>
              <h3 className={cn(
                "text-sm font-bold transition-colors duration-300",
                isDarkTheme ? "text-gray-200" : "text-foreground"
              )}>
                {getFancyWords()}
              </h3>
              <motion.div
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.2, 1]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <Sparkles
                  className="w-4 h-4"
                  style={{ color: colors.primary }}
                />
              </motion.div>
            </div>

            {/* Theme title - More prominent */}
            <h4
              className={cn(
                "text-xl font-bold mb-1 transition-colors duration-300",
                isDarkTheme ? "text-white" : "text-gray-900"
              )}
              style={{ color: isDarkTheme ? '#ffffff' : colors.primary }}
            >
              {theme.title}
            </h4>

            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Calendar className="w-3 h-3" />
              <span className={isDarkTheme ? "text-gray-400" : "text-muted-foreground"}>
                Today's featured theme
              </span>
            </div>
          </div>

          {/* Theme Toggle Switch - Only changes appearance */}
          <div className="flex flex-col items-center gap-1">
            <motion.button
              onClick={handleToggleTheme}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={cn(
                "relative inline-flex h-6 w-11 items-center rounded-full transition-all duration-300",
                isDarkTheme
                  ? "bg-gray-600"
                  : "bg-gradient-to-r from-blue-400 to-purple-500"
              )}
              aria-label={`Switch to ${isDarkTheme ? 'light' : 'dark'} theme`}
            >
              <motion.span
                animate={{
                  x: isDarkTheme ? 20 : 2
                }}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
                className="inline-block h-4 w-4 transform rounded-full bg-white shadow-lg"
              />
            </motion.button>
            <span className={cn(
              "text-xs font-medium transition-colors duration-300",
              isDarkTheme ? "text-gray-400" : "text-gray-600"
            )}>
              {isDarkTheme ? 'Dark' : 'Light'}
            </span>
          </div>
        </div>

        {/* Action button - Smaller, no hover effects */}
        <motion.button
          onClick={handleNavigateToTasks}
          whileTap={{ scale: 0.98 }}
          className={cn(
            "w-full flex items-center justify-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200",
            isDarkTheme
              ? "text-white bg-gradient-to-r from-gray-700 to-gray-800"
              : "text-white"
          )}
          style={{
            background: isDarkTheme
              ? undefined
              : `linear-gradient(135deg, ${colors.primary}, ${colors.secondary})`
          }}
        >
          <Zap className="w-3.5 h-3.5" />
          <span>Start Today's Challenge</span>
          <ArrowRight className="w-3.5 h-3.5" />
        </motion.button>

        {/* Decorative stars - Positioned better */}
        <div className="absolute top-3 right-20">
          <motion.div
            animate={{
              rotate: [0, 360],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <Star
              className={cn(
                "w-3 h-3 transition-opacity duration-300",
                isDarkTheme ? "opacity-40" : "opacity-30"
              )}
              style={{ color: colors.primary }}
            />
          </motion.div>
        </div>
      </div>
    </motion.div>
  )
}

export default TodaysThemeCard
